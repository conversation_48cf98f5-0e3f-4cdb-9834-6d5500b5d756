from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from django.db import transaction
from utils.pagination import CustomPagination
from .models import Servicefares, Route
from .serializers import ServicefaresSerializer, RouteSerializer
from mas.models import MasTrainType, MasRailwayLine
from utils.util import convert_str_to_date
from datetime import datetime


@extend_schema(
    tags=["Service Fares"]
)
class ServicefaresViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Servicefares model providing CRUD operations.
    """
    queryset = Servicefares.objects.all()
    serializer_class = ServicefaresSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['route__id','originStation','destinationStation','fareType','serviceType','fareAmount']
    search_fields = ['route__id','originStation','destinationStation','fareType','serviceType','fareAmount']
    
    def list(self, request, *args, **kwargs):
        route__id = request.query_params.get('route__id')
        originStation = request.query_params.get('originStation')
        destinationStation = request.query_params.get('destinationStation')
        fareType = request.query_params.get('fareType')
        serviceType = request.query_params.get('serviceType')
        fareAmount = request.query_params.get('fareAmount')
        ordering = request.query_params.get('ordering')     
        queryset = self.get_queryset()
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if originStation:
            queryset = queryset.filter(originStation__icontains=originStation)
        if destinationStation:
            queryset = queryset.filter(destinationStation__icontains=destinationStation)
        if fareType:
            queryset = queryset.filter(fareType=fareType)
        if serviceType:
            queryset = queryset.filter(serviceType=serviceType)
        if fareAmount:    
            queryset = queryset.filter(fareAmount=fareAmount)  
        if ordering:
            queryset = queryset.order_by(ordering)           
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_servicefares(self, request):
        """
        Import ServiceFares data from list data
        Request Body:
        [
            {
                "routeId" : "1",
                "originStation": "สถานีต้นทาง",
                "destinationStation": "สถานีปลายทาง",
                "fareType": "N",
                "fareAmount": 15.0,
                "serviceType": "N",
                "frequency": "ความถี่ของบริการ",
                "operationStart": "06:00",
                "operationEnd": "22:00"
            }
        ]

        FareType values: N=ปกติ, C=เด็ก, O=ผู้สูงอายุ
        ServiceType values: N=รถปกติ, E=รถด่วน
        """
        
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of servicefares objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not request.data:
            return Response(
                {"error": "List cannot be empty"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_servicefares = []
        errors = []

        try:
            with transaction.atomic():
                for index, item in enumerate(request.data):
                    try:
                        # Validate required fields
                        required_fields = ['routeId', 'originStation', 'destinationStation', 'fareType', 'fareAmount', 'serviceType']
                        missing_fields = [field for field in required_fields if not item.get(field)]
                        route_id = item.get('routeId')

                        if missing_fields:
                            errors.append({
                                "index": index,
                                "error": f"Missing required fields: {', '.join(missing_fields)}",
                                "data": item
                            })
                            continue

                        # Validate routeId
                        try:
                            route = Route.objects.get(id=route_id)
                        except Route.DoesNotExist:
                            errors.append({
                                "index": index,
                                "error": f"Route with id {route_id} does not exist",
                                "data": item
                            })
                            continue

                        # Validate fareType
                        valid_fare_types = ['N', 'C', 'O']
                        if item['fareType'] not in valid_fare_types:
                            item['fareType'] = 'N'

                        # Validate serviceType
                        valid_service_types = ['N', 'E']
                        if item['serviceType'] not in valid_service_types:
                            item['serviceType'] = 'N'

                        # Validate fareAmount is a number, default to 0 if cannot cast
                        try:
                            fare_amount = float(item['fareAmount'])
                            if fare_amount < 0:
                                fare_amount = 0  # Set negative values to 0
                        except (ValueError, TypeError):
                            fare_amount = 0  # Default to 0 if cannot cast to float
                        
                        # Validate operationStart and operationEnd are in correct format
                        try:
                            if item.get('operationStart'):
                                operation_start = convert_str_to_date(item['operationStart'])
                                if operation_start is None:
                                    operation_start = datetime.now()
                                if operation_start.hour < 0 or operation_start.hour > 23:
                                    operation_start = operation_start.replace(hour=0, minute=0)
                                item['operationStart'] = operation_start.strftime('%H:%M')
                        except (ValueError, TypeError):
                            item['operationStart'] = None
                        try:
                            if item.get('operationEnd'):
                                operation_end = convert_str_to_date(item['operationEnd'])
                                if operation_end is None:
                                    operation_end = datetime.now()
                                if operation_end.hour < 0 or operation_end.hour > 23:
                                    operation_end = operation_end.replace(hour=0, minute=0)
                                item['operationEnd'] = operation_end.strftime('%H:%M')
                        except (ValueError, TypeError):
                            item['operationEnd'] = None


                        # Create Servicefares object
                        servicefare_data = {
                            'route': route,
                            'originStation': item['originStation'],
                            'destinationStation': item['destinationStation'],
                            'fareType': item['fareType'],
                            'fareAmount': fare_amount,
                            'serviceType': item['serviceType'],
                            'frequency': item.get('frequency', ''),
                            'operationStart': item.get('operationStart', ''),
                            'operationEnd': item.get('operationEnd', ''),
                            'status': True  # Default status as per data dictionary
                        }

                        servicefare = Servicefares(**servicefare_data)
                        servicefare.save()

                        created_servicefares.append({
                            "id": servicefare.id,
                            "routeId": route.id,
                            "originStation": servicefare.originStation,
                            "destinationStation": servicefare.destinationStation,
                            "fareType": servicefare.fareType,
                            "fareAmount": servicefare.fareAmount,
                            "serviceType": servicefare.serviceType,
                            "frequency": servicefare.frequency,
                            "operationStart": servicefare.operationStart,
                            "operationEnd": servicefare.operationEnd
                        })

                    except Exception as e:
                        errors.append({
                            "index": index,
                            "error": f"Unexpected error: {str(e)}",
                            "data": item
                        })

                # If there are any errors, rollback the transaction
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            "error": "Import failed due to validation errors",
                            "errors": errors,
                            "created_count": 0,
                            "total_count": len(request.data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            return Response(
                {
                    "message": f"Import completed successfully. Created {len(created_servicefares)} servicefares records.",
                    "created_servicefares": created_servicefares,
                    "created_count": len(created_servicefares),
                    "total_count": len(request.data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "message": str(e),
                    "created_count": len(created_servicefares)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    tags=["Route"]
)
class RouteViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Route model providing CRUD operations.
    """
    queryset = Route.objects.all()
    serializer_class = RouteSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['masTrainType__id','masRailwayLine__id','name','origin','destination']

    def list(self, request, *args, **kwargs):
        masTrainType__id = request.query_params.get('masTrainType__id')
        masRailwayLine__id = request.query_params.get('masRailwayLine__id')
        name = request.query_params.get('name')
        origin = request.query_params.get('origin')
        destination = request.query_params.get('destination')
        ordering = request.query_params.get('ordering')
        queryset = self.get_queryset()
        if masTrainType__id:
            queryset = queryset.filter(masTrainType__id=masTrainType__id)
        if masRailwayLine__id:
            queryset = queryset.filter(masRailwayLine__id=masRailwayLine__id)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if origin:
            queryset = queryset.filter(origin__icontains=origin)
        if destination:
            queryset = queryset.filter(destination__icontains=destination)
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_routes(self, request, *args, **kwargs):
        """
        Import Route data from list data
        Request Body:
        [
            {
                "masTrainType": "ประเภทรถไฟ",
                "masRailwayLine": "สายรถไฟ",
                "name": "ชื่อเส้นทางเดินรถ",
                "origin": "สถานีต้นทาง",
                "destination": "สถานีปลายทาง"
            }
        ]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of route objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_routes = []
        errors = []

        try:
            with transaction.atomic():
                for index, item in enumerate(request.data):
                    try:
                        # Validate required fields
                        required_fields = ['masTrainType', 'masRailwayLine', 'name', 'origin', 'destination']
                        missing_fields = [field for field in required_fields if not item.get(field)]

                        if missing_fields:
                            errors.append({
                                "index": index,
                                "error": f"Missing required fields: {', '.join(missing_fields)}",
                                "data": item
                            })
                            continue

                        # Look up or create MasTrainType by name
                        mas_train_type, created = MasTrainType.objects.get_or_create(name=item['masTrainType'])

                        # Look up or create MasRailwayLine by name
                        mas_railway_line, created = MasRailwayLine.objects.get_or_create(name=item['masRailwayLine'])

                        # Create Route object
                        route_data = {
                            'masTrainType': mas_train_type,
                            'masRailwayLine': mas_railway_line,
                            'name': item['name'],
                            'origin': item['origin'],
                            'destination': item['destination'],
                            'status': True  # Default status as per data dictionary
                        }

                        route = Route(**route_data)
                        route.save()

                        created_routes.append({
                            "id": route.id,
                            "masTrainType": mas_train_type.name,
                            "masRailwayLine": mas_railway_line.name,
                            "name": route.name,
                            "origin": route.origin,
                            "destination": route.destination
                        })

                    except Exception as e:
                        errors.append({
                            "index": index,
                            "error": f"Unexpected error: {str(e)}",
                            "data": item
                        })

                # If there are any errors, rollback the transaction
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            "error": "Import failed due to validation errors",
                            "errors": errors,
                            "created_count": 0,
                            "total_count": len(request.data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            return Response(
                {
                    "message": f"Import completed successfully. Created {len(created_routes)} route records.",
                    "created_routes": created_routes,
                    "created_count": len(created_routes),
                    "total_count": len(request.data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "message": str(e),
                    "created_count": len(created_routes)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
